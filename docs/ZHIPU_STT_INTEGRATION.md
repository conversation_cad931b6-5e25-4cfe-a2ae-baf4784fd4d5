# 智谱AI语音识别服务集成文档

## 概述

本文档介绍了智谱AI语音识别服务(ZhiPuService)的集成实现，该服务基于智谱AI的GLM-ASR-2512模型，支持将音频文件转换为文本。

## API参考

智谱AI语音识别服务使用以下API端点：

```
POST https://open.bigmodel.cn/api/paas/v4/audio/transcriptions
```

### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| model | string | 是 | 模型名称，固定为 "glm-asr-2512" |
| file | file | 是 | 音频文件，支持WAV、MP3、PCM等格式 |
| stream | boolean | 否 | 是否流式返回，当前固定为false |

### 请求头

```
Authorization: Bearer {API_KEY}
Content-Type: multipart/form-data
```

## 功能特性

- **多格式支持**: 支持WAV、MP3、PCM等音频格式
- **自动格式转换**: 自动将PCM格式转换为WAV格式
- **错误重试**: 支持最多3次重试机制
- **流式接口兼容**: 虽然不支持真正的流式识别，但提供流式接口的兼容实现

## 使用方法

### 1. 配置服务

```java
// 创建配置
SysConfig config = new SysConfig();
config.setProvider("zhipu");
config.setApiKey("your-zhipu-api-key");

// 创建服务实例
ZhiPuService zhiPuService = new ZhiPuService(config);
```

### 2. 单次识别

```java
// 从文件读取音频数据
byte[] audioData = Files.readAllBytes(Paths.get("audio.wav"));

// 执行识别
String result = zhiPuService.recognition(audioData);
System.out.println("识别结果: " + result);
```

### 3. 流式识别（批量处理）

```java
// 创建音频数据流
Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();

// 发送音频数据
audioSink.tryEmitNext(audioChunk1);
audioSink.tryEmitNext(audioChunk2);
audioSink.tryEmitComplete();

// 执行识别
String result = zhiPuService.streamRecognition(audioSink);
```

## 配置说明

### 环境变量配置

```bash
export ZHIPU_API_KEY="your-api-key"
```

### 数据库配置

在系统配置表中添加智谱AI STT配置：

```sql
INSERT INTO sys_config (name, type, provider, api_key, is_enabled) 
VALUES ('智谱AI语音识别', 'stt', 'zhipu', 'your-api-key', 1);
```

## 音频格式要求

### 推荐格式
- **采样率**: 16kHz
- **位深度**: 16位
- **声道**: 单声道
- **格式**: WAV、PCM

### 支持格式
- WAV (推荐)
- MP3
- PCM

### 文件大小限制
- 最大文件大小: 25MB
- 最大时长: 30分钟

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 401 | API密钥无效 | 检查API密钥是否正确 |
| 400 | 请求参数错误 | 检查音频文件格式和大小 |
| 429 | 请求频率过高 | 降低请求频率或升级配额 |
| 500 | 服务器内部错误 | 稍后重试 |

### 重试机制

服务内置重试机制：
- 最大重试次数: 3次
- 重试间隔: 1秒
- 适用场景: 网络错误、临时服务不可用

## 性能优化

### 音频预处理
- 自动检测音频格式
- PCM到WAV格式转换
- 音频数据压缩

### 网络优化
- 连接复用
- 请求超时控制
- 自动重试机制

## 集成到STT工厂

ZhiPuService已集成到STT服务工厂中，可通过以下方式获取：

```java
@Autowired
private SttServiceFactory sttServiceFactory;

// 获取智谱AI STT服务
SysConfig config = new SysConfig();
config.setProvider("zhipu");
config.setApiKey("your-api-key");

SttService zhiPuService = sttServiceFactory.getSttService(config);
```

## 示例代码

完整的使用示例请参考：`examples/ZhiPuServiceExample.java`

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要在代码中硬编码
2. **音频质量**: 音频质量直接影响识别准确率，建议使用清晰的录音
3. **网络环境**: 需要稳定的网络连接访问智谱AI服务
4. **配额限制**: 注意API调用配额和费用
5. **流式识别**: 当前版本不支持真正的流式识别，流式接口会收集所有数据后进行批量识别

## 故障排除

### 识别失败
1. 检查API密钥是否有效
2. 确认音频文件格式正确
3. 检查网络连接
4. 查看日志中的详细错误信息

### 性能问题
1. 优化音频文件大小
2. 使用推荐的音频格式
3. 检查网络延迟

## 更新日志

- **v1.0.0**: 初始版本，支持基本的语音识别功能
- 支持多种音频格式
- 集成到STT服务工厂
- 提供完整的错误处理和重试机制
