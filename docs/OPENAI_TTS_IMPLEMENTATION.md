# OpenAI TTS 服务实现文档

## 概述

本文档描述了 OpenAI TTS (Text-to-Speech) 服务的实现，该服务集成了 OpenAI 的语音合成 API，为小智系统提供高质量的文本转语音功能。

## 实现特性

- ✅ 支持 OpenAI TTS API 集成
- ✅ 支持多种语音选择 (alloy, echo, fable, onyx, nova, shimmer)
- ✅ 支持语速调节 (0.25x - 4.0x)
- ✅ 输出 WAV 格式音频文件
- ✅ 完整的错误处理和日志记录
- ✅ 与现有 TTS 服务架构兼容

## 核心文件

### 1. OpenAIService.java
位置: `src/main/java/com/xiaozhi/dialogue/tts/providers/OpenAIService.java`

主要功能:
- 实现 `TtsService` 接口
- 调用 OpenAI TTS API
- 处理音频文件保存
- 提供错误处理和验证

### 2. TtsServiceFactory.java
位置: `src/main/java/com/xiaozhi/dialogue/tts/factory/TtsServiceFactory.java`

更新内容:
- 在 `createApiService` 方法中添加了 `"openai"` 提供商支持
- 支持通过配置创建 OpenAI TTS 服务实例

## 配置要求

### SysConfig 配置
```java
SysConfig config = new SysConfig();
config.setProvider("openai");
config.setApiKey("your-openai-api-key"); // 必需
```

### 支持的参数
- **provider**: "openai"
- **apiKey**: OpenAI API 密钥 (必需)
- **voice**: 语音类型 (可选，默认: "alloy")
- **speed**: 语速 (可选，默认: 1.0)

## 支持的语音

OpenAI TTS 支持以下语音:
- **alloy**: 中性语音
- **echo**: 男性语音
- **fable**: 英式男性语音
- **onyx**: 深沉男性语音
- **nova**: 女性语音
- **shimmer**: 女性语音

## 使用示例

### 基本使用
```java
// 创建配置
SysConfig config = new SysConfig();
config.setProvider("openai");
config.setApiKey("sk-your-api-key");

// 获取服务
TtsServiceFactory factory = new TtsServiceFactory();
TtsService openaiService = factory.getTtsService(config);

// 文本转语音
Text2SpeechParams params = new Text2SpeechParams(
    "Hello, world!",
    "alloy",
    1.0
);

String audioFilePath = openaiService.textToSpeech(params);
```

### 通过工厂类使用
```java
@Autowired
private TtsServiceFactory ttsServiceFactory;

// 获取 OpenAI TTS 服务
SysConfig config = new SysConfig();
config.setProvider("openai");
config.setApiKey("your-openai-api-key");

TtsService openaiService = ttsServiceFactory.getTtsService(config);
String audioPath = openaiService.textToSpeech(new Text2SpeechParams("测试文本", "nova", 1.2));
```

## API 规格

### textToSpeech 方法
```java
public String textToSpeech(Text2SpeechParams params) throws Exception
```

**参数:**
- `params.getText()`: 要转换的文本 (必需)
- `params.getVoice()`: 语音类型 (可选，默认: "alloy")
- `params.getSpeed()`: 语速 (可选，默认: 1.0，范围: 0.25-4.0)

**返回值:**
- 成功: 音频文件的完整路径
- 失败: 抛出异常或返回 null

**异常处理:**
- API Key 未配置: 抛出异常
- 文本为空: 返回 null
- API 调用失败: 抛出异常
- 文件写入失败: 抛出异常

## 错误处理

服务包含完整的错误处理机制:

1. **输入验证**: 检查文本内容和 API Key
2. **API 响应验证**: 检查 HTTP 状态码和响应体
3. **文件操作异常**: 处理文件写入错误
4. **详细日志**: 记录成功和失败情况

## 示例代码

完整的使用示例请参考: `examples/OpenAITtsExample.java`

该示例演示了:
- 基本文本转语音
- 不同语音测试
- 不同语速测试
- 错误处理

## 注意事项

1. **API Key 安全**: 请妥善保管 OpenAI API Key，不要在代码中硬编码
2. **费用控制**: OpenAI TTS 是付费服务，请注意使用量
3. **网络依赖**: 服务需要稳定的网络连接访问 OpenAI API
4. **文件管理**: 生成的音频文件需要定期清理以节省存储空间

## 集成状态

- ✅ 核心服务实现完成
- ✅ 工厂类集成完成
- ✅ 错误处理完成
- ✅ 示例代码完成
- ✅ 文档完成

OpenAI TTS 服务现已完全集成到小智系统中，可以通过配置使用。
