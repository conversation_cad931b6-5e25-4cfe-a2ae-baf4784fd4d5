package com.xiaozhi.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Optional;

@Data
@EqualsAndHashCode(callSuper = true)
public class MessageQueryParams extends BaseQueryParams {
    private Optional<String> type = Optional.empty();
    private Optional<String> sessionId = Optional.empty();
    private Optional<String> deviceId = Optional.empty();
    private Optional<DateRange> createdAt = Optional.empty();
}
