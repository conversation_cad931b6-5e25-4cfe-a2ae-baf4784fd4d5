package com.xiaozhi.schedule;

import cn.hutool.core.lang.hash.Hash;
import com.xiaozhi.dialogue.tts.Text2SpeechParams;
import com.xiaozhi.dialogue.tts.factory.TtsServiceFactory;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.service.SysConfigService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TestJob {

    @Resource
    private TtsServiceFactory factory;

    @Resource
    private SysConfigService configService;

    private List<String> texts = List.of(
            "我得了小红花！老师夸我作业写得棒，回家要第一个告诉爸爸妈妈～",
            "同桌不小心弄坏我的彩笔，我还没来得及用，鼻子一酸快哭了",
            "蚂蚁搬着比自己大的饼干，它们怎么这么有力气呀？我蹲在地上看半天。",
            "我养的小仓鼠走了，看着空笼子，我趴在桌上哭了好久，特别舍不得。",
            "要上台演讲了，我握着话筒的手发抖，声音也有点小，怕说错话",
            "弟弟抢我的故事书还撕了一页，我气得跺脚，眼泪在眼眶里打转",
            "不小心把同学的文具盒碰到地上，铅笔撒了一地，我赶紧道歉帮他捡起来。",
            "跑步比赛我差一点就赢了，输给同桌后，我暗下决心下次一定要超过他！",
            "弟弟总把玩具扔得满地都是，还在地上打滚蹭灰，我看着就觉得不舒服！",
            "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
            "稻（dào）— 水稻",
            "5+3=8，12-4=8",
            "你吃了饭没得哦？",
            "我今天很开心，因为妈妈给我买了新玩俱",
            "这个苹果又大又园，特别甜",
            "接下来学 “小狗”，拼音 xiǎo gǒu，名词。注意 “狗” 的发音是 gǒu，不是 gǔ哦！跟着读：小狗，xiǎo gǒu，小狗！例句：院子里有一只可爱的小狗，它在摇尾巴。和老师一起读 3 遍吧",
            "对比 “买”（mǎi）和 “卖”（mài），读音不同，意思相反哦！例句 1：妈妈给我买了新文具。例句 2：商店里卖各种各样的玩具。注意发音和意思的区别，跟着读",
            "今天上午10:00的时候我去买零食，花了我15块钱",
            "今天是2025-12-02 时间是 12:02:30 定个 中午 12 点 到 下午 3 时 15 分的闹钟",
            "这枝铅笔长 18 厘米，售价 2 元 5 角，换算成美元是$0.35 。",
            "We won the class singing contest! Everyone cheered loudly.",
            "My pet rabbit passed away. I held its empty cage and cried quietly.",
            "He scribbled on my new notebook! I shouted at him angrily.",
            "A big dog ran towards me. I froze and hid behind my dad quickly.",
            "Our class won the basketball game! I can’t believe we beat the top team",
            "He picks his nose in class. It’s gross. I don’t want to sit next to him.",
            "How does the clock work? I opened the back cover to have a look",
            "The speech contest is coming. I’m afraid I’ll forget my lines on stage.",
            "apple—A-P-P-L-E",
            "I like eat apple.",
            "He like cats very much.",
            "I like to eat appel.",
            "We go to scholl at 7:30",
            "Key word: 'schoolbag' /ˈskuːlbæg/, a noun. Split and read: school-bag, /ˈskuːlbæg/! Example: I put my books in the schoolbag. Read slowly with me!",
            "Today we compare two words: 'look' /lʊk/ (emphasize action) and 'see' /siː/ (emphasize result). Example 1: Look at the blackboard! Example 2: I see a bird in the tree. Practice distinguishing them with me!",
            "Open your bag, check your bag, put your pencil in your bag before leaving home.",
            "Count one star, count two stars, count three stars shining bright in the night sky.",
            "Write a word, spell the word, remember the word before our English test.",
            "My birthday is on June 3rd! We will have a party at 2:30 p.m. that afternoon, and I'm super happy.",
            "I eat 2 apples and 1 banana every day. Do you like fruit?",
            "How many students are there in Grade 5? There are 120—65 boys and 55 girls.",
            "爸爸，今天老师教我们说‘cat’和‘dog’，我都学会了!",
            "明天我们一家，一起去动物园看panda，我太开心了！",
            "妈妈，我想喝杯 milk，可以吗？",
            "老师让我们起一个属于自己的外国名字，我叫“LiAng”",
            "我的梦想是去 USA（美国）、UK（英国）旅行，看看 Big Ben（大本钟）和 Statue of Liberty（自由女神像）！",
            "我们的 English teacher是 Dr. White，她来自 Australia，教我们唱 ABC song",
            "Good morning, 老师！Goodbye, 同学们！明天见啦！",
            "我有一个 apple，一个 red apple，一个甜甜的 red apple，每天吃 apple 身体好",
            "I carry a book in my schoolbag，一本 story book，一本超 interesting 的 story book！I read the book every night，这个 book 太吸引人啦！",
            "今天我们学习 3 个英语单词：cat（猫）、dog（狗）、bird（鸟），请跟着读 3 遍。",
            "同学们，今天我们学单词：苹果（píng guǒ）—apple /ˈæpl/（名词）。跟着读：apple，apple，苹果！例句：I eat an apple every day.（我每天吃一个苹果。）谁能试着读一遍呀？",
            "今天学校园相关单词：图书馆（tú shū guǎn）—library /ˈlaɪbrəri/（名词）。常用搭配：go to the library（去图书馆）、read in the library（在图书馆读书）。例句：We can borrow books from the library.（我们可以从图书馆借书。）一起读搭配短语！"
    );

     @Scheduled(cron = "0 52 18 * * ?")
    // @Scheduled(fixedDelay = 30 * 1000)
    public void run() {
        var configMap = new HashMap<String, SysConfig>() {{
            put("openai", configService.selectConfigById(17));
            put("volcengine", configService.selectConfigById(2));
            put("minimax", configService.selectConfigById(5));
            put("azure", configService.selectConfigById(12));
            put("doubao", configService.selectConfigById(18));
        }};

        var voiceMap = new HashMap<String, String>() {{
            put("openai", "nova");
            put("doubao", "zh_female_vv_uranus_bigtts");
            put("volcengine", "");
            put("minimax", "Chinese (Mandarin)_Warm_Bestie");
            put("azure", "zh-CN-XiaoyiNeural");
        }};

        var platform = "doubao";
        var config = configMap.get(platform);

        var svc = factory.getTtsService(config);

        var idx = 1;
        for (var text : texts) {
            try {
                var filepath = svc.textToSpeech(new Text2SpeechParams(text, voiceMap.get(platform), 1.0, idx));
                System.out.println(STR."audio path is \{filepath}");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            idx += 1;
        }
    }

}
