package com.xiaozhi.schedule;

import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.service.SysConfigService;
import com.xiaozhi.utils.FileUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Sinks;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SttJob {

    @Resource
    private SttServiceFactory sttServiceFactory;

    @Resource
    private SysConfigService configService;

    private Map<String, String> a2t = new HashMap<>() {{
        put("asr_001.wav","你好");
        put("asr_002.wav","你好小智");
        put("asr_003.wav","你好");
        put("asr_004.wav","你好");
        put("asr_005.wav","你好");
        put("asr_006.wav","你好，hello");
        put("asr_007.wav"," 那首诗的作者是王昌龄，第三和第四个字是。美酒。请问是哪一句诗？");
        put("asr_008.wav","我最喜欢看看海贼王");
        put("asr_009.wav","。。。");
        put("asr_010.wav","你能说中文吗？");
        put("asr_011.wav","I'm fine，我想知道名侦探柯南用英语怎么说");
        put("asr_012.wav","玩一下。。古诗词。。好不好？");
        put("asr_013.wav","你能用中文和我说话吗？");
        put("asr_014.wav","还有。。呃，一首诗的作者是虞世南，最后一个字是远，请问是哪首诗？");
        put("asr_015.wav","talk show");
        put("asr_016.wav","对的对的");
        put("asr_017.wav","就是我想一个数字，然后你说如果你猜到了，那么你就输了。但是如果你猜不中，那么他这个数字的范围就会缩小。");
        put("asr_018.wav","贼眉鼠眼");
        put("asr_019.wav","叭叭叭叭叭叭叭~");
        put("asr_020.wav","扣内机哇");
        put("asr_021.wav","。。。");
        put("asr_022.wav","明天早上吃什么？");
        put("asr_023.wav","他要重启哈，不要搞不要搞不要搞，他还挺好玩的，。哼哼。他说，妈妈随时待命。");
        put("asr_024.wav","那你知道150加250等于多少吗？");
        put("asr_025.wav","那是我再考你一个，左边绿右边红，左右相遇起凉风。绿的喜欢及时雨，红的最怕水来攻，打一字。");
        put("asr_026.wav","蝴蝶， birds， 树，花");
        put("asr_027.wav","什么事儿都不管，只只顾着只顾着上课玩耍，那所以什么都不会，是不是？嗯！");
        put("asr_028.wav","嗯，我也不知道。以前我经常和呃王一晨玩，因为我每次放学我都见的是王一晨，结果嗯有一天我我。嗯，放学我又见到他，呃，呃，我跟他嗯。嗯，走到新悦城广场的时候，呃。结果发生了一件事情，你猜是啥事情？");
        put("asr_029.wav","数一下1 2 3 4 5 6 7 8 9 10 10个人");
        put("asr_030.wav","他的一生充满传奇色彩。岳飞出生于一个农民家庭，他聪明机智，勇猛善战，很快成为了一名优秀的将领。在南宋时期，经不断侵扰北部边境，岳飞奋起抵抗，成为了宋南宋的抗金名将。岳飞最著名的战役之一就是衡水之战，当时清兵围困了衡水城。岳飞带领着宋南宋军队奋营抗敌。");
        put("asr_031.wav","啊嗯，我来当火车的车头，你来当车厢，唔唔~哎！");
        put("asr_032.wav","找不到我");
        put("asr_033.wav","Play games");
        put("asr_034.wav","I'm go to the hospital.");
        put("asr_035.wav","Now I must write my home homework. See you later.");
        put("asr_036.wav","I don't like it. Another one.");
        put("asr_037.wav","Table");
        put("asr_038.wav","All right Also");
        put("asr_039.wav","byebye");
        put("asr_040.wav","Out put the flash of my racing car");
        put("asr_041.wav","Do you want to play again");
        put("asr_042.wav","hello");
        put("asr_043.wav","Do you love Peppa Pig");
        put("asr_044.wav","What's your name?");
        put("asr_045.wav","It's one and It's one and 100");
        put("asr_046.wav","yes");
        put("asr_047.wav","Do you often cook with your mom? No.");
        put("asr_048.wav","...I love me too.");
        put("asr_049.wav","I don't know. ");
        put("asr_050.wav","yesyes");
        put("asr_051.wav","I want to drink some juice");
        put("asr_052.wav","I'm Chinese.");
        put("asr_053.wav","Nice to meet you too yes");
        put("asr_054.wav","My name is. My name is Victor.");
        put("asr_055.wav","I am a pleased");
        put("asr_056.wav","So Yes. I like you");
        put("asr_057.wav","I very like a school.");
        put("asr_058.wav","I'm too busy. I have no time to look after the pets. So what a pity I have no pet.");
        put("asr_059.wav","Do you make just by yourself ？no！ Is jis grandfather and grandfather. Is cook.");
        put("asr_060.wav","I'm 5");
        put("asr_061.wav","I like judy, i like judy pra  please");
        put("asr_062.wav","I like cat");
        put("asr_063.wav","I have a ... dog");
        put("asr_064.wav","Yes i have a mouse ");
        put("asr_065.wav","Yes hes hope a lot of people ");
        put("asr_066.wav","Crazy park");
        put("asr_067.wav","I like 罗小黑战记");
        put("asr_068.wav","Will play the和平精英");
        put("asr_069.wav","教你说I like watch TV吧");
        put("asr_070.wav","I'm in山西大同");
        put("asr_071.wav","I love cartoons 我也喜欢画卡通");
        put("asr_072.wav","Is ok可是我虽然想念他，可是is ok。所以没有一直Is ok");
        put("asr_073.wav","My name is 谢细平");
        put("asr_074.wav","Do you like snakes? 是什么意思？");
        put("asr_075.wav","我先给你出一道题，从上面看有35个头，从下面看有79个角，问兔子和鸡一共有多少只？");
        put("asr_076.wav","No no one person cause. 老鹰老鹰怎么说？The other person play. 小鸡");
        put("asr_077.wav","听说老师今天又让你罚站了。Stand up. And I will. 打你了");
        put("asr_078.wav","如果你做的不好，我就要把你的玩具。patch。扔了。或者。patch 损坏。Go to school.");
        put("asr_079.wav","I will sing it to you now. 小菠萝一个小菠萝，你的真的真奇妙。你。Do you heard this song?");
        put("asr_080.wav","I play with my legos。他今天今天他说他今天拼乐高。So much fun. What about you? 他说是非常的开心。fun是开心好玩啊。What about you? 就是你呢");
        put("asr_081.wav","Do you like， do you have blue things? 这句话怎么翻译？");
        put("asr_082.wav","应该是这样说的，I don't really rely on others这样说的。你看他这个I don't。I not和don't。在really的时候是怎么用呢？");
        put("asr_083.wav","动物的动物的名称或者名词。接着说啊my favorite cat is什么什么什么知道了吧。你要把这个favorite用上，my favorite food is cat. 吃是不是猫肉");
        put("asr_084.wav"," Play with it当中的play with it分别用中文翻译成什么意思呢？");
        put("asr_085.wav","你又在玩啥呀？你刚才叫我playing games你的谁呀？");
        put("asr_086.wav","我我再往旁边画一个魔方。因为这是doctor D画的，因为他不想要让小雪肠们回家，因为他doctor D坏博士，本来就坏。");
        put("asr_087.wav","我先当一次Police，你再当一次Police好不，好不好。");
        put("asr_088.wav","14 15 16 17 18 19 20 21");
        put("asr_089.wav","我你看我在画里面画了一个直直的这个东西，嗯，我的天哪，他他跟这个尖刺也很厉害。嗯，只要嗯每次他都会出现，哎，每个世界上天空上也会出现很多呃，嗯嗯他跟这个red ball也是一样的，他们碰到。那个嗯这个直直的线他他们也会死，但是我们嗯碰到跟缩小尖刺呀，我们碰到这个直觉得我们也会缩小。");
        put("asr_090.wav","I know Chinese classical music 嗯 二泉映月。梁祝。");
        put("asr_091.wav","no。I want to have a talk with you. ");
        put("asr_092.wav","嗯 Eat some牛油果。苹果。草莓蓝莓");
        put("asr_093.wav","嗯，yes。但是但是我但是我和我和王雨晨干完了之后，我还给呃，我还用我的脚给王一晨也撕了一巴掌了一遍");
        put("asr_094.wav","你不要叫他monkey了，叫他坏monkey。");
        put("asr_095.wav","哆啦A梦的道具，我啥都有");
        put("asr_096.wav","嘟嘟嘟，拜拜，嘟嘟嘟，see you later，嘟嘟嘟");
        put("asr_097.wav","你追不上我了");
        put("asr_098.wav","怎么又在对你的friend的耍赖？");
        put("asr_099.wav","1，确实？2，Cheers？");
        put("asr_100.wav","I'm a Super super hero hero strong");
    }};

    private List<String> files = List.of(
            "asr_001.wav",
            "asr_002.wav",
            "asr_003.wav",
            "asr_004.wav",
            "asr_005.wav",
            "asr_006.wav",
            "asr_007.wav",
            "asr_008.wav",
            "asr_009.wav",
            "asr_010.wav",
            "asr_011.wav",
            "asr_012.wav",
            "asr_013.wav",
            "asr_014.wav",
            "asr_015.wav",
            "asr_016.wav",
            "asr_017.wav",
            "asr_018.wav",
            "asr_019.wav",
            "asr_020.wav",
            "asr_021.wav",
            "asr_022.wav",
            "asr_023.wav",
            "asr_024.wav",
            "asr_025.wav",
            "asr_026.wav",
            "asr_027.wav",
            "asr_028.wav",
            "asr_029.wav",
            "asr_030.wav",
            "asr_031.wav",
            "asr_032.wav",
            "asr_033.wav",
            "asr_034.wav",
            "asr_035.wav",
            "asr_036.wav",
            "asr_037.wav",
            "asr_038.wav",
            "asr_039.wav",
            "asr_040.wav",
            "asr_041.wav",
            "asr_042.wav",
            "asr_043.wav",
            "asr_044.wav",
            "asr_045.wav",
            "asr_046.wav",
            "asr_047.wav",
            "asr_048.wav",
            "asr_049.wav",
            "asr_050.wav",
            "asr_051.wav",
            "asr_052.wav",
            "asr_053.wav",
            "asr_054.wav",
            "asr_055.wav",
            "asr_056.wav",
            "asr_057.wav",
            "asr_058.wav",
            "asr_059.wav",
            "asr_060.wav",
            "asr_061.wav",
            "asr_062.wav",
            "asr_063.wav",
            "asr_064.wav",
            "asr_065.wav",
            "asr_066.wav",
            "asr_067.wav",
            "asr_068.wav",
            "asr_069.wav",
            "asr_070.wav",
            "asr_071.wav",
            "asr_072.wav",
            "asr_073.wav",
            "asr_074.wav",
            "asr_075.wav",
            "asr_076.wav",
            "asr_077.wav",
            "asr_078.wav",
            "asr_079.wav",
            "asr_080.wav",
            "asr_081.wav",
            "asr_082.wav",
            "asr_083.wav",
            "asr_084.wav",
            "asr_085.wav",
            "asr_086.wav",
            "asr_087.wav",
            "asr_088.wav",
            "asr_089.wav",
            "asr_090.wav",
            "asr_091.wav",
            "asr_092.wav",
            "asr_093.wav",
            "asr_094.wav",
            "asr_095.wav",
            "asr_096.wav",
            "asr_097.wav",
            "asr_098.wav",
            "asr_099.wav",
            "asr_100.wav"
    );

    // @Scheduled(cron = "0 39 16 * * ?")
    @Scheduled(fixedRate = 30 * 1000)
    public void run() throws IOException, InterruptedException {
        var configMap = new HashMap<String, SysConfig>() {{
            put("openai", configService.selectConfigById(17));
            put("minimax", configService.selectConfigById(5));
            put("qwen", configService.selectConfigById(19));
            put("doubao", configService.selectConfigById(9));
        }};

        var platform = "doubao";
        var svc = sttServiceFactory.getSttService(configMap.get(platform));

        var results = new ArrayList<String>();

        for (var filename : files.stream().skip(22).limit(1).toList()) {
            log.info("current file is {}", filename);
            results.forEach(log::info);
            var sink = FileUtil.readChunked(Path.of("/Users/<USER>/Downloads", filename), 1024);
            var text = svc.streamRecognition(sink);
            results.add(text);

            Thread.sleep(5 * 1000);
        }
        results.forEach(log::info);
    }

    public Sinks.Many<byte[]> urlToSink(String audioUrl) {
        // 1. 构造一个“多播、无背压溢出保护”的 Sink
        Sinks.Many<byte[]> sink = Sinks.many().multicast().onBackpressureBuffer();

        try (HttpClient client = HttpClient.newBuilder().build()) {
            HttpRequest req = HttpRequest.newBuilder()
                    .uri(URI.create(audioUrl))
                    .timeout(Duration.ofSeconds(30))
                    .GET()
                    .build();

            // 2. 异步拉取
            client.sendAsync(req, HttpResponse.BodyHandlers.ofByteArray())
                    .thenAccept(resp -> {
                        if (resp.statusCode() == 200) {
                            Sinks.EmitResult result = sink.tryEmitNext(resp.body());
                            if (result.isFailure()) {
                                sink.tryEmitError(new IllegalStateException("Emit failed: " + result));
                                return;
                            }
                            sink.tryEmitComplete();   // 3. 通知“流结束”
                        } else {
                            sink.tryEmitError(
                                    new RuntimeException("HTTP " + resp.statusCode()));
                        }
                    })
                    .exceptionally(t -> {
                        sink.tryEmitError(t);
                        return null;
                    });
        }

        return sink;   // 4. 立即返回，下游可以订阅
    }
}
