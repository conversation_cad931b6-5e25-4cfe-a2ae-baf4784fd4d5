package com.xiaozhi.dialogue.stt.providers;

import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;
import reactor.core.publisher.Sinks;

public class OpenAIService implements SttService {

    private static final String PROVIDER_NAME = "openai";
    private static final String DEFAULT_MODEL = "gpt-4o-mini-transcribe";

    private final String apiKey;
    private final String outputPath;

    public OpenAIService(SysConfig config, String outputPath) {
        this.apiKey = config.getApiKey();
        this.outputPath = outputPath;
    }

    @Override
    public String getProviderName() {
        return "openai";
    }

    @Override
    public String recognition(byte[] audioData) {
        return "";
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        var url = "https://t2a.openai.azure.com/openai/deployments/gpt-4o-mini-transcribe/audio/transcriptions?api-version=2025-03-01-preview";

    }
}
