package com.xiaozhi.dialogue.stt.providers;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.utils.AudioUtils;
import com.xiaozhi.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import reactor.core.publisher.Sinks;

import java.io.IOException;
import java.util.List;

@Slf4j
public class ZhiPuService implements SttService {

    private static final String PROVIDER_NAME = "zhipu";
    private static final String API_URL = "https://open.bigmodel.cn/api/paas/v4/audio/transcriptions";
    private static final String DEFAULT_MODEL = "glm-asr-2512";
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;

    private final String apiKey;
    private final RestTemplate restTemplate;

    public ZhiPuService(SysConfig config) {
        this.apiKey = config.getApiKey();
        this.restTemplate = new RestTemplate();
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String recognition(byte[] audioData) {
        if (audioData == null || audioData.length == 0) {
            log.warn("音频数据为空！");
            return null;
        }

        if (apiKey == null || apiKey.trim().isEmpty()) {
            log.error("智谱AI API Key未配置");
            return null;
        }

        int attempts = 0;
        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                return performRecognition(audioData);
            } catch (Exception e) {
                attempts++;
                log.warn("智谱AI STT 第{}次尝试失败: {}", attempts, e.getMessage());

                if (attempts >= MAX_RETRY_ATTEMPTS) {
                    log.error("智谱AI STT 语音识别失败，已重试{}次", MAX_RETRY_ATTEMPTS, e);
                    return null;
                }

                try {
                    Thread.sleep(RETRY_DELAY_MS);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return null;
                }
            }
        }

        return null;
    }

    private String performRecognition(byte[] audioData) throws IOException {
        // 将音频数据转换为WAV格式（如果需要）
        byte[] wavData = convertToWavIfNeeded(audioData);

        // 创建multipart请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setBearerAuth(apiKey);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("model", DEFAULT_MODEL);
        body.add("stream", false);

        // 创建文件资源
        ByteArrayResource fileResource = new ByteArrayResource(wavData) {
            @Override
            public String getFilename() {
                return "audio.wav";
            }
        };
        body.add("file", fileResource);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                API_URL,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return parseTranscriptionResponse(response.getBody());
            } else {
                log.error("智谱AI API请求失败，状态码: {}", response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("智谱AI API请求异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    private byte[] convertToWavIfNeeded(byte[] audioData) throws IOException {
        // 检查是否已经是WAV格式
        if (isWavFormat(audioData)) {
            return audioData;
        }

        // 如果不是WAV格式，假设输入是PCM格式，转换为WAV
        return pcmToWav(audioData);
    }

    private byte[] pcmToWav(byte[] pcmData) {
        // 构建WAV头部
        byte[] wavHeader = AudioUtils.buildWavHeader(pcmData, (short) 16);

        // 合并头部和PCM数据
        byte[] wavData = new byte[wavHeader.length + pcmData.length];
        System.arraycopy(wavHeader, 0, wavData, 0, wavHeader.length);
        System.arraycopy(pcmData, 0, wavData, wavHeader.length, pcmData.length);

        return wavData;
    }

    private boolean isWavFormat(byte[] audioData) {
        if (audioData.length < 12) {
            return false;
        }

        // 检查WAV文件头
        return audioData[0] == 'R' && audioData[1] == 'I' &&
               audioData[2] == 'F' && audioData[3] == 'F' &&
               audioData[8] == 'W' && audioData[9] == 'A' &&
               audioData[10] == 'V' && audioData[11] == 'E';
    }

    private String parseTranscriptionResponse(String responseBody) {
        try {
            TranscriptionResponse response = JsonUtil.fromJson(responseBody, TranscriptionResponse.class);
            if (response != null && response.getText() != null) {
                log.debug("智谱AI识别结果: {}", response.getText());
                return response.getText();
            } else {
                log.warn("智谱AI响应解析失败或结果为空: {}", responseBody);
                return null;
            }
        } catch (Exception e) {
            log.error("解析智谱AI响应失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        // 智谱AI当前不支持流式识别，将音频数据收集后进行单次识别
        log.info("智谱AI暂不支持流式识别，将收集音频数据后进行单次识别");

        try {
            // 收集所有音频数据
            var audioDataList = audioSink.asFlux()
                .collectList()
                .block();

            if (audioDataList == null || audioDataList.isEmpty()) {
                log.warn("未收到音频数据");
                return null;
            }

            // 合并音频数据
            int totalLength = audioDataList.stream().mapToInt(arr -> arr.length).sum();
            byte[] combinedAudio = new byte[totalLength];
            int offset = 0;
            for (byte[] chunk : audioDataList) {
                System.arraycopy(chunk, 0, combinedAudio, offset, chunk.length);
                offset += chunk.length;
            }

            // 执行识别
            return recognition(combinedAudio);

        } catch (Exception e) {
            log.error("智谱AI流式识别失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean supportsStreaming() {
        return false; // 智谱AI当前不支持真正的流式识别
    }

    // 响应数据模型
    @Data
    static class TranscriptionResponse {
        private String text;
        private String task;
        private String language;
        private Double duration;
        private List<Segment> segments;
    }

    @Data
    static class Segment {
        private Integer id;
        private Double start;
        private Double end;
        private String text;
        private List<Integer> tokens;
        private Double temperature;
        @JsonProperty("avg_logprob")
        private Double avgLogprob;
        @JsonProperty("compression_ratio")
        private Double compressionRatio;
        @JsonProperty("no_speech_prob")
        private Double noSpeechProb;
    }
}
