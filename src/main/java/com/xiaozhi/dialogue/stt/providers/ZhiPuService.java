package com.xiaozhi.dialogue.stt.providers;

import com.xiaozhi.dialogue.stt.SttService;
import reactor.core.publisher.Sinks;

public class ZhiPuService implements SttService {
    

    @Override
    public String getProviderName() {
        return "zhipu";
    }

    @Override
    public String recognition(byte[] audioData) {
        return "";
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        return "";
    }
}
