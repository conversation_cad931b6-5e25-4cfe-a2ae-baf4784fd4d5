package com.xiaozhi.dialogue.stt.providers;

import com.alibaba.dashscope.audio.omni.*;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.google.gson.JsonObject;
import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks;

import java.util.Base64;
import java.util.Collections;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class QwenService implements SttService {

    private final String apiKey;

    public QwenService(SysConfig config) {
        this.apiKey = config.getApiKey();
    }


    @Override
    public String getProviderName() {
        return "qwen";
    }

    @Override
    public String recognition(byte[] audioData) {
        return "";
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        try {
            return run(audioSink);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private String run(Sinks.Many<byte[]> audioSink) throws InterruptedException {
        var finishLatch = new CountDownLatch(1);

        var param = OmniRealtimeParam.builder()
                .model("qwen3-asr-flash-realtime")
                .url("wss://dashscope.aliyuncs.com/api-ws/v1/realtime")
                .apikey(apiKey)
                .build();

        final var conversationRef = new AtomicReference<OmniRealtimeConversation>(null);
        final var result = new AtomicReference<>("");
        var conversation = new OmniRealtimeConversation(param, new OmniRealtimeCallback() {
            @Override
            public void onOpen() {
                System.out.println("connection opened");
            }

            @Override
            public void onEvent(JsonObject message) {
                String type = message.get("type").getAsString();
                switch (type) {
                    case "session.created":
                        System.out.println("start session: " + message.get("session").getAsJsonObject().get("id").getAsString());
                        break;
                    case "conversation.item.input_audio_transcription.completed":
                        System.out.println("transcription: " + message.get("transcript").getAsString());
                        result.set(message.get("transcript").getAsString());
                        finishLatch.countDown();
                        break;
                    case "input_audio_buffer.speech_started":
                        System.out.println("======VAD Speech Start======");
                        break;
                    case "input_audio_buffer.speech_stopped":
                        System.out.println("======VAD Speech Stop======");
                        break;
                    case "conversation.item.input_audio_transcription.text":
                        System.out.println("transcription: " + message.get("text").getAsString());
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onClose(int code, String reason) {
                System.out.println("connection closed code: " + code + ", reason: " + reason);
            }
        });
        conversationRef.set(conversation);

        try {
            conversation.connect();
        } catch (NoApiKeyException e) {
            throw new RuntimeException(e);
        }

        var transcriptionParam = new OmniRealtimeTranscriptionParam();
        transcriptionParam.setLanguage("zh");
        transcriptionParam.setInputAudioFormat("pcm");
        transcriptionParam.setInputSampleRate(16000);
        // transcriptionParam.setCorpusText("");

        var config = OmniRealtimeConfig.builder()
                .modalities(Collections.singletonList(OmniRealtimeModality.TEXT))
                .transcriptionConfig(transcriptionParam)
                .enableTurnDetection(false)
                .build();
        conversation.updateSession(config);

        audioSink.asFlux()
                .subscribe(chunk -> {
                    conversation.appendAudio(Base64.getEncoder().encodeToString(chunk));
                });

        conversation.commit();
        finishLatch.await(10, TimeUnit.SECONDS);

        log.info("task finished");

        conversation.close(1000, "bye");

        return result.get();
    }
}
