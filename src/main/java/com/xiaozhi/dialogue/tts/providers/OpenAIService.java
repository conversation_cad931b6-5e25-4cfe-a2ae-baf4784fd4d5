package com.xiaozhi.dialogue.tts.providers;

import com.xiaozhi.dialogue.tts.Text2SpeechParams;
import com.xiaozhi.dialogue.tts.TtsService;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.utils.HttpUtil;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.api.OpenAiAudioApi;
import org.springframework.http.HttpHeaders;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.Map;

@Slf4j
public class OpenAIService implements TtsService {

    private static final String PROVIDER_NAME = "openai";
    private static final String DEFAULT_MODEL = "gpt-4o-mini-tts";

    private final String apiKey;
    private final String outputPath;

    public OpenAIService(SysConfig config, String outputPath) {
        this.apiKey = config.getApiKey();
        this.outputPath = outputPath;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String textToSpeech(Text2SpeechParams params) throws Exception {
        if (params.getText() == null || params.getText().isEmpty()) {
            log.warn("文本内容为空！");
            return null;
        }

        if (apiKey == null || apiKey.isEmpty()) {
            throw new Exception("OpenAI API Key 未配置");
        }

        // 生成音频文件名和路径
        String audioFileName = params.getIdx()  + "." + audioFormat();
        String audioFilePath = Paths.get(outputPath, audioFileName).toString();
        var url = STR."https://t2a.openai.azure.com/openai/deployments/gpt-4o-mini-tts/audio/speech?api-version=2025-03-01-preview";

        var body = Map.of(
                "model", DEFAULT_MODEL,
                "voice", params.getVoice(),
                "input", params.getText(),
                "instructions", """
                        Voice: High-energy, upbeat, and encouraging, projecting enthusiasm and motivation.
                        
                        Punctuation: Short, punchy sentences with strategic pauses to maintain excitement and clarity.
                        
                        Delivery: Fast-paced and dynamic, with rising intonation to build momentum and keep engagement high.
                        
                        Phrasing: Action-oriented and direct, using motivational cues to push participants forward.
                        
                        Tone: Positive, energetic, and empowering, creating an atmosphere of encouragement and achievement.
                        """
        );
        log.info("body is {}", body);
        var headers = new HttpHeaders();
        headers.setBearerAuth(apiKey);

        return HttpUtil.post(url, headers, body, byte[].class)
                .flatMapTry(data -> {
                    // 将音频数据写入文件
                    try (FileOutputStream fos = new FileOutputStream(audioFilePath)) {
                        fos.write(data);
                        fos.flush();
                    }

                    return Try.success(audioFilePath);
                })
                .onSuccess(_ -> log.info("OpenAI TTS 合成完成，文件保存至: {}", audioFilePath))
                .onFailure(e -> log.error("OpenAI TTS 文件写入失败", e))
                .getOrElse("");
    }
}
