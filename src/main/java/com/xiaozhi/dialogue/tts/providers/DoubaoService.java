package com.xiaozhi.dialogue.tts.providers;

import com.google.gson.JsonParser;
import com.xiaozhi.dialogue.tts.Text2SpeechParams;
import com.xiaozhi.dialogue.tts.TtsService;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.utils.AudioUtils;
import com.xiaozhi.utils.OkHttpUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.UUID;

@Slf4j
public class DoubaoService implements TtsService {

    // 音频输出路径
    private String outputPath;

    // API相关
    private String appId;
    private String accessToken; // 对应 apiKey

    private final OkHttpClient client = OkHttpUtil.client;

    private static final String API_URL = "https://openspeech.bytedance.com/api/v3/tts/unidirectional";
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    public DoubaoService(SysConfig config, String outputPath) {
        this.outputPath = outputPath;
        this.appId = config.getAppId();
        this.accessToken = config.getApiKey();
    }

    @Override
    public String audioFormat() {
        return "mp3";
    }

    @Override
    public String getProviderName() {
        return "doubao";
    }

    @Override
    public String textToSpeech(Text2SpeechParams params) throws Exception {
        if (params.getText() == null || params.getText().isEmpty()) {
            log.warn("文本内容为空！");
            return null;
        }

        try {
            // 生成音频文件名
            String audioFileName = STR."\{params.getIdx()}.\{audioFormat()}";
            String audioFilePath = outputPath + audioFileName;

            // 发送POST请求
            sendRequest(params, audioFilePath);
            return audioFilePath;
        } catch (Exception e) {
            log.error("语音合成时发生错误！", e);
            throw e;
        }
    }

    /**
     * 发送POST请求到火山引擎API，获取语音合成结果
     */
    private void sendRequest(Text2SpeechParams params, String audioFilePath) throws Exception {
        try {

            var body = STR."""
                    {
                      "user": {
                        "uid": "\{UUID.randomUUID().toString()}"
                      },
                      "req_params": {
                        "text": "\{params.getText()}",
                        "speaker": "\{params.getVoice()}",
                        "audio_params": {
                          "format": "mp3",
                          "sample_rate": \{AudioUtils.SAMPLE_RATE}
                        }
                      }
                    }
                    """;

            var requestBody = RequestBody.create(body, JSON);

            // 设置请求头和请求体
            var request = new Request.Builder()
                    .url(API_URL)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("X-Api-App-Id", appId)
                    .addHeader("X-Api-Access-Key", accessToken)
                    .addHeader("X-Api-Resource-Id", "seed-tts-2.0")
                    .addHeader("X-Api-Request-Id", UUID.randomUUID().toString())
                    .post(requestBody)
                    .build();

            client.newCall(request).enqueue(new Callback(){
                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                    final var audios = new ArrayList<byte[]>();

                    try (var body = response.body()) {
                        var source = body.source();
                        while (!source.exhausted()) {
                            var line = source.readUtf8Line();
                            if (line == null) break;
                            log.info("line =======> {}", line);
                            var data = JsonParser.parseString(line).getAsJsonObject();

                            // 检查响应是否包含错误
                            if (data.has("code") && data.get("code").getAsInt() == 0 && !data.get("data").isJsonNull()) {
                                log.info("b64 data =====> {}", data.get("data"));
                                var b64data = data.get("data").getAsString();
                                if (b64data != null) {
                                    audios.add(Base64.getDecoder().decode(b64data));
                                }
                            }
                        }
                    }

                    // 保存音频文件
                    var audioFile = new File(audioFilePath);
                    try (var outputStream = new FileOutputStream(audioFile)) {
                        for (byte[] audio : audios) {
                            outputStream.write(audio);
                        }
                    }
                }

                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {
            log.error("发送TTS请求时发生错误", e);
            throw new Exception("发送TTS请求失败", e);
        }
    }

}
