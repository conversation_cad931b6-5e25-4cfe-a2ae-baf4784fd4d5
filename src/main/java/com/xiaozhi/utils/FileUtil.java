package com.xiaozhi.utils;

import reactor.core.publisher.Sinks;
import reactor.core.scheduler.Schedulers;

import java.nio.ByteBuffer;
import java.nio.channels.AsynchronousFileChannel;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;

public class FileUtil {


    public static Sinks.Many<byte[]> readChunked(Path file, int chunkSize) {
        Sinks.Many<byte[]> sink =
                Sinks.many().replay().all();

        Schedulers.boundedElastic().schedule(() -> {
            try (AsynchronousFileChannel channel =
                         AsynchronousFileChannel.open(file, StandardOpenOption.READ)) {

                long size = channel.size();
                long position = 0;
                while (position < size) {
                    int len = (int) Math.min(chunkSize, size - position);
                    ByteBuffer buf = ByteBuffer.allocate(len);
                    channel.read(buf, position).get();          // 同步等这一片读完
                    buf.flip();
                    byte[] out = new byte[buf.remaining()];
                    buf.get(out);

                    Sinks.EmitResult r = sink.tryEmitNext(out);
                    if (r.isFailure()) {
                        sink.tryEmitError(new RuntimeException("Emit failed: " + r));
                        return;
                    }
                    position += len;
                }
                sink.tryEmitComplete();
            } catch (Exception e) {
                sink.tryEmitError(e);
            }
        });
        return sink;
    }

}
