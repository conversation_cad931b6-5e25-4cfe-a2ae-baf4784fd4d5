package examples;

import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;

/**
 * 智谱AI语音识别服务集成测试
 * 验证ZhiPuService是否正确集成到STT服务工厂中
 */
public class ZhiPuServiceIntegrationTest {
    
    public static void main(String[] args) {
        System.out.println("智谱AI语音识别服务集成测试");
        System.out.println("================================");
        
        try {
            // 测试1: 验证服务工厂能否创建ZhiPuService
            testServiceFactory();
            
            // 测试2: 验证服务基本属性
            testServiceProperties();
            
            // 测试3: 验证错误处理
            testErrorHandling();
            
            System.out.println("\n✅ 所有测试通过！ZhiPuService已成功集成。");
            
        } catch (Exception e) {
            System.err.println("\n❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试服务工厂创建ZhiPuService
     */
    private static void testServiceFactory() {
        System.out.println("\n1. 测试服务工厂创建ZhiPuService...");
        
        // 创建配置
        SysConfig config = new SysConfig();
        config.setProvider("zhipu");
        config.setApiKey("test-api-key");
        config.setId(1);
        
        // 创建服务工厂
        SttServiceFactory factory = new SttServiceFactory();
        
        // 获取服务
        SttService service = factory.getSttService(config);
        
        // 验证服务类型
        if (service.getClass().getSimpleName().equals("ZhiPuService")) {
            System.out.println("   ✅ 服务工厂成功创建ZhiPuService");
        } else {
            throw new RuntimeException("服务工厂创建的不是ZhiPuService，而是: " + service.getClass().getSimpleName());
        }
        
        // 验证提供商名称
        if ("zhipu".equals(service.getProviderName())) {
            System.out.println("   ✅ 提供商名称正确: " + service.getProviderName());
        } else {
            throw new RuntimeException("提供商名称错误，期望: zhipu，实际: " + service.getProviderName());
        }
    }
    
    /**
     * 测试服务基本属性
     */
    private static void testServiceProperties() {
        System.out.println("\n2. 测试服务基本属性...");
        
        SysConfig config = new SysConfig();
        config.setProvider("zhipu");
        config.setApiKey("test-api-key");
        config.setId(2);
        
        SttServiceFactory factory = new SttServiceFactory();
        SttService service = factory.getSttService(config);
        
        // 测试流式识别支持
        if (!service.supportsStreaming()) {
            System.out.println("   ✅ 正确报告不支持流式识别");
        } else {
            throw new RuntimeException("ZhiPuService不应该支持流式识别");
        }
        
        // 测试提供商名称
        String providerName = service.getProviderName();
        if ("zhipu".equals(providerName)) {
            System.out.println("   ✅ 提供商名称正确: " + providerName);
        } else {
            throw new RuntimeException("提供商名称错误: " + providerName);
        }
    }
    
    /**
     * 测试错误处理
     */
    private static void testErrorHandling() {
        System.out.println("\n3. 测试错误处理...");
        
        // 测试空音频数据
        SysConfig config = new SysConfig();
        config.setProvider("zhipu");
        config.setApiKey("test-api-key");
        config.setId(3);
        
        SttServiceFactory factory = new SttServiceFactory();
        SttService service = factory.getSttService(config);
        
        // 测试null音频数据
        String result1 = service.recognition(null);
        if (result1 == null) {
            System.out.println("   ✅ 正确处理null音频数据");
        } else {
            throw new RuntimeException("应该返回null，但返回了: " + result1);
        }
        
        // 测试空音频数据
        String result2 = service.recognition(new byte[0]);
        if (result2 == null) {
            System.out.println("   ✅ 正确处理空音频数据");
        } else {
            throw new RuntimeException("应该返回null，但返回了: " + result2);
        }
        
        // 测试无API密钥的情况
        SysConfig configNoKey = new SysConfig();
        configNoKey.setProvider("zhipu");
        configNoKey.setApiKey(null);
        configNoKey.setId(4);
        
        SttService serviceNoKey = factory.getSttService(configNoKey);
        byte[] dummyAudio = createDummyAudio();
        String result3 = serviceNoKey.recognition(dummyAudio);
        if (result3 == null) {
            System.out.println("   ✅ 正确处理缺少API密钥的情况");
        } else {
            throw new RuntimeException("应该返回null，但返回了: " + result3);
        }
    }
    
    /**
     * 创建测试用的音频数据
     */
    private static byte[] createDummyAudio() {
        // 创建简单的PCM音频数据
        byte[] audio = new byte[1024];
        for (int i = 0; i < audio.length; i++) {
            audio[i] = (byte) (Math.sin(i * 0.1) * 127);
        }
        return audio;
    }
}
