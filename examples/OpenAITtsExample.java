package examples;

import com.xiaozhi.dialogue.tts.Text2SpeechParams;
import com.xiaozhi.dialogue.tts.TtsService;
import com.xiaozhi.dialogue.tts.factory.TtsServiceFactory;
import com.xiaozhi.entity.SysConfig;

import java.io.File;

/**
 * OpenAI TTS 服务使用示例
 * 
 * 演示如何使用 OpenAI TTS 服务进行文本转语音
 */
public class OpenAITtsExample {

    public static void main(String[] args) {
        try {
            // 创建 TTS 服务工厂
            TtsServiceFactory ttsServiceFactory = new TtsServiceFactory();
            
            // 创建 OpenAI TTS 配置
            SysConfig config = new SysConfig();
            config.setProvider("openai");
            config.setApiKey("your-openai-api-key-here"); // 请替换为实际的 OpenAI API Key
            
            // 获取 OpenAI TTS 服务
            TtsService openaiService = ttsServiceFactory.getTtsService(config);
            
            System.out.println("=== OpenAI TTS 服务信息 ===");
            System.out.println("服务提供商: " + openaiService.getProviderName());
            System.out.println("音频格式: " + openaiService.audioFormat());
            System.out.println("支持流式TTS: " + openaiService.isSupportStreamTts());
            
            // 测试基本文本转语音
            System.out.println("\n=== 基本文本转语音测试 ===");
            Text2SpeechParams params = new Text2SpeechParams(
                "Hello, this is a test of OpenAI text-to-speech service. 你好，这是OpenAI文本转语音服务的测试。",
                "alloy", // OpenAI 支持的语音: alloy, echo, fable, onyx, nova, shimmer
                1.0      // 语速
            );
            
            String audioFilePath = openaiService.textToSpeech(params);
            
            if (audioFilePath != null) {
                File audioFile = new File(audioFilePath);
                if (audioFile.exists()) {
                    System.out.println("✅ 音频文件生成成功!");
                    System.out.println("文件路径: " + audioFilePath);
                    System.out.println("文件大小: " + audioFile.length() + " 字节");
                } else {
                    System.out.println("❌ 音频文件不存在: " + audioFilePath);
                }
            } else {
                System.out.println("❌ 音频文件生成失败");
            }
            
            // 测试不同语音
            System.out.println("\n=== 不同语音测试 ===");
            String[] voices = {"alloy", "echo", "fable", "onyx", "nova", "shimmer"};
            
            for (String voice : voices) {
                try {
                    Text2SpeechParams voiceParams = new Text2SpeechParams(
                        "This is " + voice + " voice speaking.",
                        voice,
                        1.0
                    );
                    
                    String voiceAudioPath = openaiService.textToSpeech(voiceParams);
                    if (voiceAudioPath != null && new File(voiceAudioPath).exists()) {
                        System.out.println("✅ " + voice + " 语音生成成功: " + voiceAudioPath);
                    } else {
                        System.out.println("❌ " + voice + " 语音生成失败");
                    }
                } catch (Exception e) {
                    System.out.println("❌ " + voice + " 语音生成异常: " + e.getMessage());
                }
            }
            
            // 测试不同语速
            System.out.println("\n=== 不同语速测试 ===");
            double[] speeds = {0.5, 1.0, 1.5, 2.0};
            
            for (double speed : speeds) {
                try {
                    Text2SpeechParams speedParams = new Text2SpeechParams(
                        "This is speech at " + speed + "x speed.",
                        "alloy",
                        speed
                    );
                    
                    String speedAudioPath = openaiService.textToSpeech(speedParams);
                    if (speedAudioPath != null && new File(speedAudioPath).exists()) {
                        System.out.println("✅ " + speed + "x 语速生成成功: " + speedAudioPath);
                    } else {
                        System.out.println("❌ " + speed + "x 语速生成失败");
                    }
                } catch (Exception e) {
                    System.out.println("❌ " + speed + "x 语速生成异常: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.err.println("OpenAI TTS 示例运行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
