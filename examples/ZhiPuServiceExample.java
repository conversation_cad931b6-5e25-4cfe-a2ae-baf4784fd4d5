package examples;

import com.xiaozhi.dialogue.stt.providers.ZhiPuService;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.utils.AudioUtils;
import reactor.core.publisher.Sinks;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 智谱AI语音识别服务使用示例
 * 
 * 使用前请确保：
 * 1. 已获取智谱AI API Key
 * 2. 准备好测试音频文件（支持WAV、MP3、PCM格式）
 */
public class ZhiPuServiceExample {
    
    public static void main(String[] args) {
        // 配置API Key - 请替换为您的实际API Key
        String apiKey = System.getenv("ZHIPU_API_KEY");
        if (apiKey == null || apiKey.trim().isEmpty()) {
            System.err.println("请设置环境变量 ZHIPU_API_KEY");
            System.err.println("export ZHIPU_API_KEY=\"your-api-key\"");
            return;
        }
        
        try {
            // 创建配置
            SysConfig config = new SysConfig();
            config.setProvider("zhipu");
            config.setApiKey(apiKey);
            
            // 创建智谱AI STT服务实例
            ZhiPuService zhiPuService = new ZhiPuService(config);
            
            System.out.println("智谱AI语音识别服务示例");
            System.out.println("服务提供商: " + zhiPuService.getProviderName());
            System.out.println("支持流式识别: " + zhiPuService.supportsStreaming());
            System.out.println();
            
            // 示例1: 使用字节数组进行识别
            System.out.println("=== 示例1: 字节数组识别 ===");
            testWithByteArray(zhiPuService);
            
            // 示例2: 从文件读取音频进行识别
            System.out.println("\n=== 示例2: 文件音频识别 ===");
            testWithAudioFile(zhiPuService);
            
            // 示例3: 流式识别（实际上是批量处理）
            System.out.println("\n=== 示例3: 流式识别 ===");
            testStreamRecognition(zhiPuService);
            
        } catch (Exception e) {
            System.err.println("示例运行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 使用字节数组进行识别测试
     */
    private static void testWithByteArray(ZhiPuService service) {
        try {
            // 创建测试音频数据（1秒440Hz正弦波）
            byte[] audioData = createTestAudio();
            
            System.out.println("创建测试音频数据: " + audioData.length + " 字节");
            
            // 执行识别
            long startTime = System.currentTimeMillis();
            String result = service.recognition(audioData);
            long endTime = System.currentTimeMillis();
            
            System.out.println("识别耗时: " + (endTime - startTime) + "ms");
            System.out.println("识别结果: " + (result != null ? result : "识别失败"));
            
        } catch (Exception e) {
            System.err.println("字节数组识别测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 从文件读取音频进行识别测试
     */
    private static void testWithAudioFile(ZhiPuService service) {
        try {
            // 检查是否有测试音频文件
            String[] testFiles = {"test.wav", "test.mp3", "audio/test.wav", "audio/test.mp3"};
            String audioFile = null;
            
            for (String file : testFiles) {
                if (Files.exists(Paths.get(file))) {
                    audioFile = file;
                    break;
                }
            }
            
            if (audioFile == null) {
                System.out.println("未找到测试音频文件，跳过文件识别测试");
                System.out.println("请将测试音频文件放在以下位置之一: " + String.join(", ", testFiles));
                return;
            }
            
            System.out.println("使用音频文件: " + audioFile);
            
            // 读取音频文件
            byte[] audioData = Files.readAllBytes(Paths.get(audioFile));
            System.out.println("音频文件大小: " + audioData.length + " 字节");
            
            // 执行识别
            long startTime = System.currentTimeMillis();
            String result = service.recognition(audioData);
            long endTime = System.currentTimeMillis();
            
            System.out.println("识别耗时: " + (endTime - startTime) + "ms");
            System.out.println("识别结果: " + (result != null ? result : "识别失败"));
            
        } catch (Exception e) {
            System.err.println("文件音频识别测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 流式识别测试
     */
    private static void testStreamRecognition(ZhiPuService service) {
        try {
            // 创建音频数据流
            Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();
            
            // 创建测试音频数据并分块发送
            byte[] fullAudio = createTestAudio();
            int chunkSize = 1024;
            
            System.out.println("模拟流式发送音频数据，总大小: " + fullAudio.length + " 字节");
            
            // 在后台线程中发送音频数据
            Thread.startVirtualThread(() -> {
                try {
                    for (int i = 0; i < fullAudio.length; i += chunkSize) {
                        int end = Math.min(i + chunkSize, fullAudio.length);
                        byte[] chunk = new byte[end - i];
                        System.arraycopy(fullAudio, i, chunk, 0, chunk.length);
                        
                        audioSink.tryEmitNext(chunk);
                        Thread.sleep(10); // 模拟实时传输延迟
                    }
                    audioSink.tryEmitComplete();
                } catch (Exception e) {
                    audioSink.tryEmitError(e);
                }
            });
            
            // 执行流式识别
            long startTime = System.currentTimeMillis();
            String result = service.streamRecognition(audioSink);
            long endTime = System.currentTimeMillis();
            
            System.out.println("流式识别耗时: " + (endTime - startTime) + "ms");
            System.out.println("识别结果: " + (result != null ? result : "识别失败"));
            
        } catch (Exception e) {
            System.err.println("流式识别测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建测试音频数据（PCM格式，16kHz，16位，单声道）
     */
    private static byte[] createTestAudio() {
        int sampleRate = 16000;
        int duration = 2; // 2秒
        int samples = sampleRate * duration;
        byte[] pcmData = new byte[samples * 2]; // 16位 = 2字节每样本
        
        // 生成440Hz正弦波（A4音符）
        for (int i = 0; i < samples; i++) {
            double time = (double) i / sampleRate;
            short sample = (short) (Short.MAX_VALUE * 0.3 * Math.sin(2 * Math.PI * 440 * time));
            
            // 小端序存储
            pcmData[i * 2] = (byte) (sample & 0xFF);
            pcmData[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
        }
        
        return pcmData;
    }
}
